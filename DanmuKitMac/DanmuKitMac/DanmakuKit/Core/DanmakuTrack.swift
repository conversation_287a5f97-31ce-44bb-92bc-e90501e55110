//
//  DanmakuTrack.swift
//  DanmuKitMac
//
//  Created by Augment Agent on 2025/8/22.
//  Ported from DanmakuKit iOS
//

import Cocoa

protocol DanmakuTrack {

    var positionY: CGFloat { get set }

    var index: UInt { get set }

    var stopClosure: ((_ cell: DanmakuCell) -> Void)? { get set }

    var danmakuCount: Int { get }

    var isOverlap: Bool { get set }

    var playingSpeed: Float { get set }

    init(view: NSView)

    func shoot(danmaku: DanmakuCell)

    func canShoot(danmaku: DanmakuCellModel) -> Bool

    func play()

    func pause()

    func stop()

    func pause(_ danmaku: DanmakuCellModel) -> Bool

    func play(_ danmaku: DanmakuCellModel) -> Bool

    func sync(_ danmaku: DanmakuCell, at progress: Float)

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float)

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool

    func clean()

}

class FloatingDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard danmaku.model != nil else { return }
        cells.append(danmaku)
        // 在 macOS 的 layer-backed NSView 中，不能仅改 layer.position；需要更新 view 的 frame
        // 初始放在右侧视图外，Y 使用轨道中心 positionY
        if let vw = view {
            let originX = vw.bounds.width
            let originY = positionY - danmaku.bounds.height / 2.0
            danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        }
        danmaku.model?.track = index
        prepare(danmaku: danmaku)
        addAnimation(to: danmaku)
        danmaku.enterTrack()
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        guard !isOverlap else { return true }
        guard let cell = cells.last else { return true }
        guard let cellModel = cell.model else { return true }

        let viewWidth = view?.bounds.width ?? 0
        let preWidth = viewWidth + cell.frame.width
        let nextWidth = viewWidth + danmaku.size.width
        let preRight = max(cell.realFrame.maxX, 0)
        let preCellTime = min(preRight / preWidth * CGFloat(cellModel.displayTime), CGFloat(cellModel.displayTime))
        let distance = viewWidth - preRight - 10
        guard distance >= 0 else { return false }
        let preV = preWidth / CGFloat(cellModel.displayTime)
        let nextV = nextWidth / CGFloat(danmaku.displayTime)
        if nextV - preV <= 0 { return true }
        let time = (distance / (nextV - preV))
        return !(time < preCellTime)
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        let totalWidth = (view?.bounds.width ?? 0) + danmaku.frame.width
        let rate = max(danmaku.frame.maxX / totalWidth, 0)
        danmaku.animationBeginTime = CFAbsoluteTimeGetCurrent()

        let animation = CABasicAnimation(keyPath: "position.x")
        animation.beginTime = CACurrentMediaTime()
        animation.duration = (model.displayTime * Double(rate)) / Double(max(0.0001, playingSpeed))
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        // 用 frame.midX 作为 from，避免受 layer.position 未同步影响
        animation.fromValue = NSNumber(value: Float(danmaku.frame.midX))
        animation.toValue = NSNumber(value: Float(-danmaku.bounds.width))
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "floating")
    }

    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                // 如果layer之前被暂停了(speed为0)，需要从暂停位置继续
                if layer.speed == 0.0 {
                    let pausedTime = layer.timeOffset
                    layer.timeOffset = 0
                    layer.beginTime = 0
                    let timeSincePause = layer.convertTime(CACurrentMediaTime(), from: nil) - pausedTime
                    layer.beginTime = timeSincePause
                }
                layer.speed = 1.0
            }
        }
    }

    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        let totalWidth = view!.frame.width + danmaku.bounds.width
        let syncFrame = CGRect(x: view!.frame.width - totalWidth * CGFloat(progress), y: positionY - danmaku.bounds.height / 2.0, width: danmaku.bounds.width, height: danmaku.bounds.height)
        cells.append(danmaku)
        danmaku.layer?.opacity = 1
        danmaku.frame = syncFrame
        danmaku.model?.track = index
        danmaku.animationTime = model.displayTime * Double(progress)
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return canShoot(danmaku: danmaku)
    }

    func clean() {
        stop()
    }

}

class TopDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime
        danmaku.model?.track = index
        let originX = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        return cells.isEmpty
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.0
        animation.duration = model.displayTime / Double(playingSpeed)
        animation.beginTime = CACurrentMediaTime() + model.displayTime * 0.8 / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "top")
    }

    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                layer.speed = 1.0
                layer.timeOffset = 0
            }
        }
    }

    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime * Double(progress)
        danmaku.model?.track = index
        let originX = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX, y: originY, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return cells.isEmpty
    }

    func clean() {
        stop()
    }

}

class BottomDanmakuTrack: DanmakuTrack {

    var positionY: CGFloat = 0

    var index: UInt = 0

    var stopClosure: ((DanmakuCell) -> Void)?

    var danmakuCount: Int {
        return cells.count
    }

    var isOverlap = false

    var playingSpeed: Float = 1.0

    private weak var view: NSView?

    private var cells: [DanmakuCell] = []

    required init(view: NSView) {
        self.view = view
    }

    func shoot(danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime
        danmaku.model?.track = index
        let originX_b = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY_b = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX_b, y: originY_b, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
        danmaku.enterTrack()
        addAnimation(to: danmaku)
    }

    func canShoot(danmaku: DanmakuCellModel) -> Bool {
        return cells.isEmpty
    }

    private func addAnimation(to danmaku: DanmakuCell) {
        guard let model = danmaku.model else { return }

        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = 1.0
        animation.toValue = 0.0
        animation.duration = model.displayTime / Double(playingSpeed)
        animation.beginTime = CACurrentMediaTime() + model.displayTime * 0.8 / Double(playingSpeed)
        animation.timingFunction = CAMediaTimingFunction(name: .easeIn)
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards

        animation.delegate = AnimationDelegate { [weak self, weak danmaku] finished in
            guard let self = self, let danmaku = danmaku else { return }
            if finished {
                danmaku.leaveTrack()
                self.stopClosure?(danmaku)
                if let index = self.cells.firstIndex(of: danmaku) {
                    self.cells.remove(at: index)
                }
            }
        }

        danmaku.layer?.add(animation, forKey: "bottom")
    }

    func play() {
        cells.forEach { cell in
            if let layer = cell.layer {
                layer.speed = 1.0
                layer.timeOffset = 0
            }
        }
    }

    func pause() {
        cells.forEach { cell in
            if let layer = cell.layer {
                let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
                layer.speed = 0.0
                layer.timeOffset = pausedTime
            }
        }
    }

    func stop() {
        cells.forEach {
            $0.removeFromSuperview()
            $0.layer?.removeAllAnimations()
        }
        cells.removeAll()
    }

    func pause(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            let pausedTime = layer.convertTime(CACurrentMediaTime(), from: nil)
            layer.speed = 0.0
            layer.timeOffset = pausedTime
        }
        return true
    }

    func play(_ danmaku: DanmakuCellModel) -> Bool {
        guard let cell = cells.first(where: { $0.model?.identifier == danmaku.identifier }) else { return false }
        if let layer = cell.layer {
            layer.speed = 1.0
            layer.timeOffset = 0
        }
        return true
    }

    func sync(_ danmaku: DanmakuCell, at progress: Float) {
        guard let model = danmaku.model else { return }
        cells.append(danmaku)
        danmaku.animationTime = model.displayTime * Double(progress)
        danmaku.model?.track = index
        let originX_b = (view!.bounds.width - danmaku.bounds.width) / 2.0
        let originY_b = positionY - danmaku.bounds.height / 2.0
        danmaku.frame = CGRect(x: originX_b, y: originY_b, width: danmaku.bounds.width, height: danmaku.bounds.height)
        danmaku.layer?.opacity = 1
    }

    func syncAndPlay(_ danmaku: DanmakuCell, at progress: Float) {
        sync(danmaku, at: progress)
        addAnimation(to: danmaku)
    }

    func canSync(_ danmaku: DanmakuCellModel, at progress: Float) -> Bool {
        return cells.isEmpty
    }

    func clean() {
        stop()
    }

}

// Match iOS helper
private func prepare(danmaku: DanmakuCell) {
    danmaku.animationTime = 0
    danmaku.animationBeginTime = 0
    danmaku.layer?.opacity = 1
}


// Animation delegate helper
class AnimationDelegate: NSObject, CAAnimationDelegate {
    private let completion: (Bool) -> Void

    init(completion: @escaping (Bool) -> Void) {
        self.completion = completion
        super.init()
    }

    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        completion(flag)
    }
}
