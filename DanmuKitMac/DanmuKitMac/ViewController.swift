//
//  ViewController.swift
//  DanmuKitMac
//
//  Created by 常超群 on 2025/8/22.
//

import Cocoa

class ViewController: NSViewController {

    private var danmakuView: DanmakuView!
    private var timer: Timer?
    private var danmakuIndex = 0

    private let sampleTexts = [
        "欢迎来到macOS弹幕世界！",
        "这是一个测试弹幕",
        "支持多种弹幕类型",
        "浮动弹幕从右到左",
        "顶部弹幕固定显示",
        "底部弹幕也很酷",
        "弹幕可以重叠显示",
        "支持不同颜色和字体",
        "macOS弹幕库移植成功！",
        "点击弹幕试试看"
    ]

    private var playButton: NSButton!
    private var pauseButton: NSButton!
    private var stopButton: NSButton!
    private var shootButton: NSButton!
    private var cleanButton: NSButton!
    private var overlapCheckbox: NSButton!
    private var speedSlider: NSSlider!
    private var speedLabel: NSTextField!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupDanmakuView()
        setupControls()
        setupTimer()
    }

    private func setupDanmakuView() {
        // Create danmaku view
        danmakuView = DanmakuView(frame: NSRect(x: 20, y: 100, width: 600, height: 300))
        danmakuView.delegate = self
        danmakuView.enableCellReusable = true
        danmakuView.trackHeight = 20

        // Add bright background color for visibility testing
        danmakuView.wantsLayer = true
        danmakuView.layer?.backgroundColor = NSColor.black.withAlphaComponent(1.0).cgColor
        danmakuView.layer?.cornerRadius = 8
        danmakuView.layer?.borderColor = NSColor.purple.cgColor
        danmakuView.layer?.borderWidth = 1

        view.addSubview(danmakuView)
    }

    private func setupControls() {
        // Create control buttons if they don't exist in storyboard
        if playButton == nil {
            createControlButtons()
        }

        playButton?.target = self
        playButton?.action = #selector(playButtonClicked)

        pauseButton?.target = self
        pauseButton?.action = #selector(pauseButtonClicked)

        stopButton?.target = self
        stopButton?.action = #selector(stopButtonClicked)

        shootButton?.target = self
        shootButton?.action = #selector(shootButtonClicked)

        cleanButton?.target = self
        cleanButton?.action = #selector(cleanButtonClicked)

        overlapCheckbox?.target = self
        overlapCheckbox?.action = #selector(overlapCheckboxChanged)

        speedSlider?.target = self
        speedSlider?.action = #selector(speedSliderChanged)
        speedSlider?.minValue = 0.5
        speedSlider?.maxValue = 3.0
        speedSlider?.doubleValue = 1.0

        updateSpeedLabel()
    }

    private func createControlButtons() {
        let buttonWidth: CGFloat = 80
        let buttonHeight: CGFloat = 30
        let spacing: CGFloat = 10
        let startX: CGFloat = 20
        let startY: CGFloat = 50

        playButton = NSButton(frame: NSRect(x: startX, y: startY, width: buttonWidth, height: buttonHeight))
        playButton.title = "Play"
        playButton.bezelStyle = .rounded
        view.addSubview(playButton)

        pauseButton = NSButton(frame: NSRect(x: startX + (buttonWidth + spacing), y: startY, width: buttonWidth, height: buttonHeight))
        pauseButton.title = "Pause"
        pauseButton.bezelStyle = .rounded
        view.addSubview(pauseButton)

        stopButton = NSButton(frame: NSRect(x: startX + 2 * (buttonWidth + spacing), y: startY, width: buttonWidth, height: buttonHeight))
        stopButton.title = "Stop"
        stopButton.bezelStyle = .rounded
        view.addSubview(stopButton)

        shootButton = NSButton(frame: NSRect(x: startX + 3 * (buttonWidth + spacing), y: startY, width: buttonWidth, height: buttonHeight))
        shootButton.title = "Shoot"
        shootButton.bezelStyle = .rounded
        view.addSubview(shootButton)

        cleanButton = NSButton(frame: NSRect(x: startX + 4 * (buttonWidth + spacing), y: startY, width: buttonWidth, height: buttonHeight))
        cleanButton.title = "Clean"
        cleanButton.bezelStyle = .rounded
        view.addSubview(cleanButton)

        overlapCheckbox = NSButton(frame: NSRect(x: startX + 5 * (buttonWidth + spacing), y: startY, width: 100, height: buttonHeight))
        overlapCheckbox.title = "Overlap"
        overlapCheckbox.setButtonType(.switch)
        view.addSubview(overlapCheckbox)

        speedSlider = NSSlider(frame: NSRect(x: startX, y: startY - 40, width: 200, height: 20))
        view.addSubview(speedSlider)

        speedLabel = NSTextField(frame: NSRect(x: startX + 210, y: startY - 40, width: 100, height: 20))
        speedLabel.isEditable = false
        speedLabel.isBordered = false
        speedLabel.backgroundColor = NSColor.clear
        view.addSubview(speedLabel)
    }

    private func setupTimer() {
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.shootRandomDanmaku()
        }
    }

    private func shootRandomDanmaku() {
        guard danmakuView.status == .play else { return }

        let text = sampleTexts[danmakuIndex % sampleTexts.count]
        danmakuIndex += 1

        let model = DanmakuTextCellModel(text: text)
        model.type = .floating  // 只使用浮动弹幕进行测试

        model.textColor = .white
        model.font = .systemFont(ofSize: 16)

        // 开启黑色描边
        model.strokeColor = .black
        model.strokeWidth = 0.0
        model.strokeOpacity = 0.0

        // 轻阴影增加可读性
        model.shadowColor = .black
        model.shadowOpacity = 0.4
        model.shadowBlur = 1.5
        model.shadowOffset = CGSize(width: 1, height: 1)

        model.calculateSize()
        

        danmakuView.shoot(danmaku: model)
        print("弹幕出现: \(text)")
    }

    @objc private func playButtonClicked() {
        danmakuView.play()
        print("Danmaku started playing")
    }

    @objc private func pauseButtonClicked() {
        danmakuView.pause()
        print("Danmaku paused")
    }

    @objc private func stopButtonClicked() {
        danmakuView.stop()
        timer?.invalidate()
        timer = nil
        setupTimer()
        print("Danmaku stopped")
    }

    @objc private func shootButtonClicked() {
        let text = "手动发射的弹幕 #\(danmakuIndex)"
        danmakuIndex += 1

        let model = DanmakuTextCellModel(text: text)
        // 手动发射的弹幕用蓝色以示区别，但保持默认描边和阴影
        model.textColor = NSColor.systemBlue
        model.font = NSFont.boldSystemFont(ofSize: 16)
        model.calculateSize()

        danmakuView.shoot(danmaku: model)
        print("Manual danmaku shot: \(text)")
    }

    @objc private func cleanButtonClicked() {
        danmakuView.clean()
        print("Danmaku cleaned")
    }

    @objc private func overlapCheckboxChanged() {
        danmakuView.isOverlap = overlapCheckbox.state == .on
        print("Overlap mode: \(danmakuView.isOverlap)")
    }

    @objc private func speedSliderChanged() {
        danmakuView.playingSpeed = Float(speedSlider.doubleValue)
        updateSpeedLabel()
        print("Speed changed to: \(danmakuView.playingSpeed)")
    }

    private func updateSpeedLabel() {
        speedLabel.stringValue = String(format: "Speed: %.1fx", speedSlider.doubleValue)
    }

    override var representedObject: Any? {
        didSet {
        // Update the view, if already loaded.
        }
    }

    deinit {
        timer?.invalidate()
    }

}

// MARK: - DanmakuViewDelegate
extension ViewController: DanmakuViewDelegate {

    func danmakuView(_ danmakuView: DanmakuView, willDisplay danmaku: DanmakuCell) {
        print("Will display danmaku: \((danmaku.model as? DanmakuTextCellModel)?.text ?? "unknown")")
    }

    func danmakuView(_ danmakuView: DanmakuView, didEndDisplaying danmaku: DanmakuCell) {
        // print("Did end displaying danmaku: \((danmaku.model as? DanmakuTextCellModel)?.text ?? "unknown")")
    }

    func danmakuView(_ danmakuView: DanmakuView, didClicked danmaku: DanmakuCell) {
        let text = (danmaku.model as? DanmakuTextCellModel)?.text ?? "unknown"
        print("Danmaku clicked: \(text)")

        // Show alert when danmaku is clicked
        let alert = NSAlert()
        alert.messageText = "弹幕被点击了！"
        alert.informativeText = "你点击了弹幕：\(text)"
        alert.addButton(withTitle: "确定")
        alert.runModal()
    }

    func danmakuView(_ danmakuView: DanmakuView, noSpaceShoot danmaku: DanmakuCellModel) {
        print("No space to shoot danmaku: \((danmaku as? DanmakuTextCellModel)?.text ?? "unknown")")
    }

    func danmakuView(_ danmakuView: DanmakuView, dequeueReusable danmaku: DanmakuCell) {
        print("Reusing danmaku cell")
    }

}

// MARK: - DanmakuCellType Extension
extension DanmakuCellType: CaseIterable {
    public static var allCases: [DanmakuCellType] {
        return [.floating, .top, .bottom]
    }
}

